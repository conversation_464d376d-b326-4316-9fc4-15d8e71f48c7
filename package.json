{"name": "dis-bot", "version": "3.2.0", "private": true, "main": "./index.js", "description": "Dynamic Innovative Studio Discord Main Work Bot.", "keywords": ["discord", "bot", "work", "dis"], "homepage": "https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot#readme", "repository": {"type": "git", "url": "https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot.git"}, "bugs": {"url": "https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot/issues"}, "license": "Commercial", "type": "module", "engines": {"node": ">=22.15.0 <23.0.0"}, "scripts": {"prepare": "npx husky install", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:js": "eslint . --max-warnings=100", "check": "npm run format:check && npm run lint:js && npm run test", "ci": "npm run check", "start:prod": "node dist/index.js", "start": "node launch.js", "dev": "nodemon launch.js", "build": "esbuild index.js --bundle --platform=node --external:dotenv/config --external:semver --external:@sentry/node --external:discord.js --external:bottleneck --external:pino --external:pino-pretty --external:mysql2 --external:mysql2/promise --outdir=dist --format=esm --sourcemap", "new:cmds": "node src/deploy-commands.js", "test": "node scripts/test-runner.js", "test:unit": "node scripts/test-runner.js --unit", "test:integration": "node scripts/test-runner.js --integration", "test:e2e": "node scripts/test-runner.js --e2e", "test:watch": "node scripts/test-runner.js --watch", "test:coverage": "node scripts/test-runner.js --coverage", "test:no-coverage": "node scripts/test-runner.js --no-coverage", "test:verbose": "node scripts/test-runner.js --verbose", "test:silent": "node scripts/test-runner.js --silent", "test:bail": "node scripts/test-runner.js --bail", "test:jest": "NODE_OPTIONS=\"--experimental-vm-modules\" jest --coverage", "test:jest:watch": "NODE_OPTIONS=\"--experimental-vm-modules\" jest --watch", "coverage:report": "node scripts/simple-coverage-report.js", "coverage:unified": "node scripts/coverage-report-unified.js", "coverage:open": "open coverage/index.html", "coverage:trends": "node scripts/coverage-trend-tracker.js analyze", "coverage:track": "node scripts/coverage-trend-tracker.js add", "docker:build": "docker build -t dis-discord-bot .", "docker:build:dev": "docker build -f Dockerfile.dev -t dis-discord-bot:dev .", "docker:run": "docker run --env-file .env -p 3000:3000 dis-discord-bot", "docker:run:dev": "docker run --env-file .env -p 3000:3000 -v $(pwd):/app dis-discord-bot:dev", "docker:compose:up": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:compose:logs": "docker-compose logs -f", "docker:compose:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:compose:dev:down": "docker-compose -f docker-compose.dev.yml down", "docker:compose:dev:logs": "docker-compose -f docker-compose.dev.yml logs -f", "deploy:staging": "./scripts/deploy.sh --environment staging", "deploy:production": "./scripts/deploy.sh --environment production", "deploy:dev": "docker-compose -f docker-compose.dev.yml up -d", "release": "semantic-release", "audit": "npm audit", "audit:fix": "npm audit fix", "pre-commit": "lint-staged"}, "lint-staged": {"*.{js,mjs,cjs}": ["eslint --fix"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"@sentry/node": "^9.42.1", "bottleneck": "^2.19.5", "discord.js": "^14.21.0", "dotenv": "^17.2.1", "execa": "^9.6.0", "mysql2": "^3.14.2", "node-fetch": "^3.3.2", "node-ipc": "^12.0.0", "pino": "9.7.0", "semver": "^7.7.2", "strong-type": "^1.1.0", "synckit": "^0.11.11"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^10.0.0", "@jest/globals": "^30.0.5", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@sentry/types": "^9.42.1", "@types/babel__core": "^7.20.5", "@types/eslint": "^9.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/prettier": "^3.0.0", "@types/semver": "^7.7.0", "@types/typescript": "^2.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "cosmiconfig": "^9.0.0", "cosmiconfig-typescript-loader": "^6.1.0", "esbuild": "^0.25.8", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^3.0.4", "eslint-plugin-unicorn": "^60.0.0", "globals": "^16.3.0", "husky": "^9.1.7", "jest": "^30.0.5", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "pino-pretty": "^13.0.0", "prettier": "^3.6.2", "semantic-release": "^24.2.5", "typescript": "^5.8.3"}, "packageManager": "yarn@4.9.2", "resolutions": {"@pkgr/core": "0.2.7", "synckit": "0.11.8"}}