/**
 * @file MESSAGE.JS
 *
 * @version 3.0.3
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command to send embedded direct messages to specific members.
 * Allows administrators and HR to DM a specified user by mention or username.
 * Includes input validation, rate limiting, and robust error handling.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import {
  DiscordAPIError,
  EmbedBuilder,
  MessageFlags,
  SlashCommandBuilder,
} from 'discord.js';

import { botConfig } from '../../config/config.js';
import { messages } from '../../config/messages.js';
import { CommandMiddleware } from '../../middleware/commandAuthentication.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import logger from '../../utils/logger.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').Client} DiscordClient
 * @typedef {import('discord.js').User} DiscordUser
 */

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('message')
  .setDescription(
    'Send a direct message to a co-worker via mention or username'
  )
  .addUserOption(option =>
    option
      .setName('co-worker')
      .setDescription('Co-worker to message (mention or username)')
      .setRequired(true)
  )
  .addStringOption(option =>
    option
      .setName('message')
      .setDescription('The message to send')
      .setRequired(true)
  );

// ------------ UTILITY FUNCTION
/**
 * Attempts to send a DM to a Discord user.
 * @param {DiscordClient} client - Discord client instance
 * @param {string} userId - Discord user ID to message
 * @param {string} content - Message content to send
 * @returns {Promise<{success: boolean, error?: string, code?: number|string, stack?: string}>}
 */
async function sendDirectMessage(client, userId, content) {
  try {
    const discordUser = await client.users.fetch(userId);
    const embed = new EmbedBuilder()
      .setDescription(content)
      .setColor(parseInt(botConfig.colors.primary.replace(/^#/, ''), 16))
      .setTimestamp();
    await discordUser.send({ embeds: [embed] });
    return { success: true };
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    const code =
      error instanceof DiscordAPIError ? error.code : 'NON_DISCORD_ERROR';
    return {
      success: false,
      error: err.message,
      code: code,
      stack: err.stack,
    };
  }
}

// ------------ MAIN EXECUTE FUNCTION
/**
 * Executes the message command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction object
 * @param {DiscordClient} client - The Discord client instance
 * @returns {Promise<void>}
 */
async function _execute(interaction, client) {
  try {
    // Rate limit check for message command
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'message',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `You're being rate limited. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Global cooldown logic
    const cooldownKey = `message:${interaction.user.id}`;
    const messageCooldown = cooldown.getCooldownMs('message');
    if (!cooldown.isExpired(cooldownKey, messageCooldown)) {
      return await safeReply(interaction, {
        content: `⏳ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, messageCooldown) / 1000)}s before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const user = interaction.options.getUser('co-worker');
    const content = interaction.options.getString('message');

    // Validate user and userId
    if (!user || !/^\d{17,20}$/.test(user.id)) {
      return await safeReply(interaction, {
        content: 'Invalid user ID provided.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Validate message content and length for embed description
    if (!content || content.length > 4096) {
      return await safeReply(interaction, {
        content: !content
          ? 'Message content is required.'
          : 'Message content exceeds 4096 characters.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Validate for non-printable/control characters
    // eslint-disable-next-line no-control-regex
    if (/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(content)) {
      return await safeReply(interaction, {
        content: 'Message contains invalid or control characters.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

    const result = await sendDirectMessage(client, user.id, content);

    if (result.success) {
      cooldown.set(cooldownKey);
      logger.info(
        {
          event: 'admin_message',
          command: 'message',
          user: interaction.user.id,
          username: interaction.user.tag,
          target: user.id,
          targetTag: user.tag,
          guild: interaction.guild?.id,
        },
        `Admin ${interaction.user.tag} sent DM to ${user.tag}`
      );
      await safeReply(interaction, {
        content: messages.admin.messageSent.replace('{user}', user.tag ?? ''),
      });
    } else if (result.code === 10013) {
      logger.warn(
        {
          event: 'admin_message',
          command: 'message',
          user: interaction.user.id,
          username: interaction.user.tag,
          target: user.id,
          targetTag: user.tag,
          guild: interaction.guild?.id,
          code: String(result.code),
        },
        `User ${user.tag} (${user.id}) not found`
      );
      await safeReply(interaction, {
        content: `Could not locate user ${user.tag ?? ''}`,
      });
    } else if (result.code === 50007) {
      logger.warn(
        {
          event: 'admin_message',
          command: 'message',
          user: interaction.user.id,
          username: interaction.user.tag,
          target: user.id,
          targetTag: user.tag,
          guild: interaction.guild?.id,
          code: String(result.code),
        },
        `DM blocked by user ${user.tag}`
      );
      await safeReply(interaction, {
        content: messages.errors.dmFailed,
      });
    } else {
      logger.error(
        {
          event: 'admin_message',
          command: 'message',
          user: interaction.user.id,
          username: interaction.user.tag,
          target: user.id,
          targetTag: user.tag,
          guild: interaction.guild?.id,
          code:
            typeof result.code === 'string' ? result.code : String(result.code),
          stack: result.stack,
          component: 'message-command',
        },
        `DM failed to ${user.tag}: ${result.error}`
      );
      await safeReply(interaction, {
        content: messages.errors.internalError,
      });
    }
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    await handleError(err, { command: 'message' }, client, {
      critical: true,
    });
    await safeReply(interaction, {
      content: messages.errors.internalError,
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ COMMAND SECURITY
export const execute = CommandHandler.standard(
  'message',
  CommandMiddleware.hrAndAdmin(_execute)
);

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard(
    'message',
    CommandMiddleware.hrAndAdmin(_execute)
  ),
  manualCooldown: true,
};
