/**
 * @file LOCKDOWN.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Admin command to temporarily lock down a channel by restricting message sending.
 * Allows HR and admin users to prevent members from sending messages for a specified duration.
 * Includes automatic unlock functionality and proper permission management.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import {
  EmbedBuilder,
  MessageFlags,
  PermissionFlagsBits,
  SlashCommandBuilder,
} from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandMiddleware } from '../../middleware/commandAuthentication.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import logger from '../../utils/logger.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').GuildChannel} GuildChannel
 * @typedef {import('discord.js').TextChannel} TextChannel
 * @typedef {import('discord.js').PermissionOverwrites} PermissionOverwrites
 */

// ------------ LOCKDOWN UTILITIES
/**
 * Parse time duration string into milliseconds.
 * @param {string} timeStr - Time string (e.g., "5m", "1h", "30s")
 * @returns {number|null} - Duration in milliseconds or null if invalid
 */
function parseTimeToMs(timeStr) {
  const match = timeStr.match(/^(\d+)([smhd])$/i);
  if (!match) return null;

  const value = parseInt(match[1]);
  const unit = match[2].toLowerCase();

  switch (unit) {
    case 's':
      return value * 1000;
    case 'm':
      return value * 60 * 1000;
    case 'h':
      return value * 60 * 60 * 1000;
    case 'd':
      return value * 24 * 60 * 60 * 1000;
    default:
      return null;
  }
}

/**
 * Format milliseconds into human-readable duration.
 * @param {number} ms - Duration in milliseconds
 * @returns {string} - Formatted duration string
 */
function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days}d ${hours % 24}h`;
  if (hours > 0) return `${hours}h ${minutes % 60}m`;
  if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
  return `${seconds}s`;
}

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('lockdown')
  .setDescription(
    'Lock down a channel to prevent members from sending messages 🔒'
  )
  .addChannelOption(option =>
    option
      .setName('channel')
      .setDescription('Channel to lock down (default: current channel)')
      .setRequired(false)
  )
  .addStringOption(option =>
    option
      .setName('duration')
      .setDescription('Duration (e.g., 5m, 1h, 30s, 2d)')
      .setRequired(true)
  )
  .addStringOption(option =>
    option
      .setName('reason')
      .setDescription('Reason for the lockdown')
      .setRequired(false)
      .setMaxLength(500)
  );

/**
 * Execute the lockdown command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'lockdown',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `🔒 Lockdown command is rate limited! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `lockdown:${interaction.user.id}`;
    const lockdownCooldown = cooldown.getCooldownMs('lockdown');
    if (!cooldown.check(cooldownKey, lockdownCooldown)) {
      return await safeReply(interaction, {
        content: `🔒 Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, lockdownCooldown) / 1000)}s before using lockdown again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get options
    const targetChannel =
      interaction.options.getChannel('channel') ?? interaction.channel;
    const durationStr = interaction.options.getString('duration', true);
    const reason =
      interaction.options.getString('reason') ?? 'No reason provided';

    // Validate channel
    if (!targetChannel || !targetChannel.isTextBased()) {
      return await safeReply(interaction, {
        content: '🔒 Invalid channel! Please select a text channel.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Parse duration
    const durationMs = parseTimeToMs(durationStr);
    if (
      !durationMs ||
      durationMs < 1000 ||
      durationMs > 7 * 24 * 60 * 60 * 1000
    ) {
      return await safeReply(interaction, {
        content:
          '🔒 Invalid duration! Use format like "5m", "1h", "30s", "2d" (max 7 days).',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Check bot permissions
    const botMember = interaction.guild?.members.me;
    if (
      !botMember ||
      !targetChannel
        .permissionsFor(botMember)
        ?.has(PermissionFlagsBits.ManageChannels)
    ) {
      return await safeReply(interaction, {
        content: "🔒 I don't have permission to manage this channel!",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get the @everyone role
    const everyoneRole = interaction.guild?.roles.everyone;
    if (!everyoneRole) {
      return await safeReply(interaction, {
        content: '🔒 Unable to find the @everyone role!',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Apply lockdown by denying send messages permission
    await targetChannel.permissionOverwrites.edit(
      everyoneRole,
      {
        SendMessages: false,
      },
      {
        reason: `Lockdown by ${interaction.user.tag}: ${reason}`,
      }
    );

    // Schedule unlock
    setTimeout(async () => {
      try {
        await targetChannel.permissionOverwrites.edit(
          everyoneRole,
          {
            SendMessages: null, // Reset to default
          },
          {
            reason: `Automatic unlock after lockdown duration`,
          }
        );

        // Send unlock notification
        if (targetChannel.isTextBased()) {
          const unlockEmbed = new EmbedBuilder()
            .setColor(parseInt(botConfig.colors.success.replace(/^#/, ''), 16))
            .setTitle('🔓 Channel Unlocked')
            .setDescription('The lockdown has been automatically lifted.')
            .setTimestamp();

          await targetChannel.send({ embeds: [unlockEmbed] });
        }
      } catch (error) {
        logger.error('Failed to automatically unlock channel:', error);
      }
    }, durationMs);

    // Create success embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.warning.replace(/^#/, ''), 16))
      .setTitle('🔒 Channel Locked Down')
      .setDescription('The channel has been successfully locked down.')
      .addFields(
        {
          name: '📍 Channel',
          value: targetChannel.toString(),
          inline: true,
        },
        {
          name: '⏰ Duration',
          value: formatDuration(durationMs),
          inline: true,
        },
        {
          name: '👮 Moderator',
          value: interaction.user.toString(),
          inline: true,
        },
        {
          name: '📝 Reason',
          value: reason,
          inline: false,
        },
        {
          name: '🔓 Auto-Unlock',
          value: `<t:${Math.floor((Date.now() + durationMs) / 1000)}:R>`,
          inline: false,
        }
      )
      .setFooter({
        text: `Dynamic Innovative Studio • ${botConfig.version}`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    await safeReply(interaction, { embeds: [embed] });

    // Send notification to the locked channel
    if (
      targetChannel.id !== interaction.channelId &&
      targetChannel.isTextBased()
    ) {
      const channelEmbed = new EmbedBuilder()
        .setColor(parseInt(botConfig.colors.warning.replace(/^#/, ''), 16))
        .setTitle('🔒 Channel Locked')
        .setDescription(
          `This channel has been locked down by ${interaction.user.toString()}`
        )
        .addFields(
          {
            name: '⏰ Duration',
            value: formatDuration(durationMs),
            inline: true,
          },
          {
            name: '📝 Reason',
            value: reason,
            inline: false,
          }
        )
        .setTimestamp();

      await targetChannel.send({ embeds: [channelEmbed] });
    }

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'lockdown' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content: '🔒 Failed to lock down the channel. Please try again later.',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard(
    'lockdown',
    CommandMiddleware.hrAndAdmin(_execute)
  ),
};
