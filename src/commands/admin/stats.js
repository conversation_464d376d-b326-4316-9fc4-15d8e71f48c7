// @ts-check
/**
 * @file STATISTICS.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command to retrieve and display bot statistics like /stats but way more detailed and
 * informative, this command is inteded for bot admins and developers.
 * Provides information about the bot's performance, usage, and other relevant metrics.
 * Supports various display formats, including embeds and plain text.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import {
  EmbedBuilder,
  MessageFlags,
  PermissionFlagsBits,
  SlashCommandBuilder,
} from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandMiddleware } from '../../middleware/commandAuthentication.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import { defaultCache } from '../../utils/cacheManagement.js';
import * as cooldown from '../../utils/cooldown.js';
import db from '../../utils/Database.js';
import { handleError } from '../../utils/errorHandler.js';
import {
  checkRateLimit,
  getRateLimitStatus,
  rateLimit,
} from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 */

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('stats')
  .setDescription('Get bot statistics and performance metrics.')
  .setDefaultMemberPermissions(PermissionFlagsBits.Administrator);

// ------------ COMMAND EXECUTION
/**
 * Executes the stats command.
 * Shows bot and API latency in an embed.
 *
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check for stats command
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'stats',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `You're being rate limited. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const cooldownKey = `stats:${interaction.user.id}`;
    const statsCooldown = cooldown.getCooldownMs('stats');
    if (!cooldown.check(cooldownKey, statsCooldown)) {
      return await safeReply(interaction, {
        content: `⏳ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, statsCooldown) / 1000)}s before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Latency and API latency
    const latency = Date.now() - interaction.createdTimestamp;
    const apiLatency = Math.round(interaction.client.ws.ping);

    // General
    const guildCount = interaction.client.guilds.cache.size;
    const userSet = new Set();
    interaction.client.guilds.cache.forEach(guild => {
      guild.members.cache.forEach(member => {
        if (!member.user.bot) userSet.add(member.user.id);
      });
    });
    const userCount = userSet.size;
    const commandCount =
      interaction.client.application?.commands?.cache?.size ?? 'N/A';

    // System
    const uptime = process.uptime();
    const os = await import('os');
    const load = os.loadavg();
    const mem = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();

    // Database (MySQL pool)
    let dbStats = {};
    if (db && db.pool && typeof db.pool.getConnection === 'function') {
      try {
        const [rows] = await db.pool.query(
          'SHOW STATUS WHERE `variable_name` = "Threads_connected"'
        );
        dbStats = {
          connections: rows && rows[0] ? rows[0].Value : 'N/A',
          free: 'N/A',
          queue: 'N/A',
        };
      } catch {
        dbStats = {
          connections: 'N/A',
          free: 'N/A',
          queue: 'N/A',
        };
      }
    }

    // Cache
    let cacheStats = {};
    if (defaultCache && typeof defaultCache.getStats === 'function') {
      cacheStats = defaultCache.getStats();
    }

    // Rate Limits
    const rlStats = rateLimit.getStats ? rateLimit.getStats() : {};
    const rlTracked = rateLimit.requests ? rateLimit.requests.size : 'N/A';
    const rlBypass = Array.isArray(rateLimit.options?.bypassRoles)
      ? rateLimit.options.bypassRoles.length
      : 'N/A';

    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.primary.replace(/^#/, ''), 16))
      .setTitle('📊 Bot Statistics')
      .addFields(
        {
          name: 'General',
          value:
            `Guilds: \`${guildCount}\`\n` +
            `Cached Users: \`${userCount}\`\n` +
            `Commands: \`${commandCount}\``,
          inline: true,
        },
        {
          name: 'System',
          value:
            `Uptime: \`${Math.floor(uptime / 60)}m ${Math.floor(uptime % 60)}s\`\n` +
            `Load Avg: \`${load.map(n => n.toFixed(2)).join(', ')}\`\n` +
            `Memory: \`${(mem.rss / 1024 / 1024).toFixed(1)}MB RSS\`\n` +
            `Free/Total Mem: \`${(freeMem / 1024 / 1024).toFixed(1)}/${(totalMem / 1024 / 1024).toFixed(1)}MB\``,
          inline: true,
        },
        {
          name: 'Database',
          value:
            `Pool: \`${dbStats.connections ?? 'N/A'}\` connections\n` +
            `Free: \`${dbStats.free ?? 'N/A'}\`\n` +
            `Queue: \`${dbStats.queue ?? 'N/A'}\``,
          inline: true,
        },
        {
          name: 'Cache',
          value:
            cacheStats && Object.keys(cacheStats).length
              ? `Entries: \`${cacheStats.entries ?? 'N/A'}\`\nHits: \`${cacheStats.hits ?? 'N/A'}\`\nMisses: \`${cacheStats.misses ?? 'N/A'}\``
              : 'No cache stats available',
          inline: true,
        },
        {
          name: 'Rate Limits',
          value:
            `Tracked: \`${rlTracked}\`\n` +
            `Bypass Roles: \`${rlBypass}\`\n` +
            `Limited: \`${rlStats.limited ?? 'N/A'}\`\n` +
            `Total: \`${rlStats.total ?? 'N/A'}\``,
          inline: true,
        },
        {
          name: 'Latency',
          value: `Bot: \`${latency}ms\`\nAPI: \`${apiLatency}ms\``,
          inline: true,
        }
      )
      .setFooter({ text: `Dynamic Innovative Studio • ${botConfig.version}` });

    await safeReply(interaction, { embeds: [embed] });
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'stats' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content: '⚠️ **System Error!** Please contact Founder & CEO immediately.',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ COMMAND SECURITY
export const execute = CommandHandler.standard(
  'stats',
  CommandMiddleware.adminOnly(_execute)
);

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('stats', _execute, {
    manualCooldown: true,
  }),
};
