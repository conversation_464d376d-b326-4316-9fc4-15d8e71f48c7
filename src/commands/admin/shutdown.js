/**
 * @file SHUTDOWN.JS
 *
 * @version 3.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command to shut down the D.I.S Bot.
 * Intended for use by administrators only.
 * Sends a confirmation message and then shuts down the bot process.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import {
  EmbedBuilder,
  MessageFlags,
  PermissionFlagsBits,
  SlashCommandBuilder,
} from 'discord.js';

import { botConfig } from '../../config/config.js';
import messages from '../../config/messages.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import logger from '../../utils/logger.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').Client} DiscordClient
 */

/**
 * @typedef {Object} GlobalThis
 * @property {(event: string, payload: any) => void} [sendIpcEvent] - IPC event sender function
 */

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('shutdown')
  .setDescription('Completely shutdown the D.I.S Bot')
  .setDefaultMemberPermissions(PermissionFlagsBits.Administrator);

// ------------ MAIN EXECUTE FUNCTION
/**
 * Executes the shutdown command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction object
 * @param {DiscordClient} client - The Discord client instance
 * @returns {Promise<void>}
 */
async function _execute(interaction, client) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'shutdown',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `You're being rate limited. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const cooldownKey = `shutdown:${interaction.user.id}`;
    const shutdownCooldown = cooldown.getCooldownMs('shutdown');
    if (!cooldown.isExpired(cooldownKey, shutdownCooldown)) {
      return await safeReply(interaction, {
        content: `⏳ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, shutdownCooldown) / 1000)}s before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

    // Create embed message for shutdown
    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.error.replace(/^#/, ''), 16))
      .setTitle('🔌 Shutting Down')
      .setDescription('Bot is now powering off...')
      .setFooter({
        text: `Initiated by ${interaction.user.tag}`,
        iconURL: interaction.user.displayAvatarURL({ forceStatic: false }),
      })
      .setTimestamp();

    await safeReply(interaction, { embeds: [embed] });

    logger.info(
      {
        event: 'admin_shutdown',
        command: 'shutdown',
        user: interaction.user.id,
        username: interaction.user.tag,
        guild: interaction.guild?.id,
      },
      `Admin-initiated shutdown by ${interaction.user.tag} (${interaction.user.id})`
    );

    // Gracefully destroy the client and exit the process
    /** @type {GlobalThis} */
    const globalThisTyped = /** @type {GlobalThis} */ (globalThis);
    if (typeof globalThisTyped.sendIpcEvent === 'function') {
      globalThisTyped.sendIpcEvent('shutdown', {
        initiator: {
          id: interaction.user.id,
          tag: interaction.user.tag,
        },
        timestamp: Date.now(),
      });
      logger.info(
        {
          event: 'admin_shutdown',
          command: 'shutdown',
          user: interaction.user.id,
          username: interaction.user.tag,
          guild: interaction.guild?.id,
        },
        'Shutdown signal sent to launcher via IPC'
      );
    } else {
      if (client && typeof client.destroy === 'function') {
        await client.destroy();
      }
      process.exit(0);
    }

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'shutdown' },
      client,
      {
        critical: true,
      }
    );
    await safeReply(interaction, {
      content: messages.errors.internalError,
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.admin('shutdown', _execute),
};
