/**
 * @file ROLE.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command for role management - allows HR and admin users to add or remove roles from members.
 * Provides secure role management with proper permission checks and audit logging.
 * Supports adding and removing roles with comprehensive error handling.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import {
  MessageFlags,
  PermissionFlagsBits,
  SlashCommandBuilder,
} from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandMiddleware } from '../../middleware/commandAuthentication.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import logger from '../../utils/logger.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';
import {
  hasAdminPermission,
  hasHumanResourcesPermission,
} from '../../utils/validators.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').GuildMember} GuildMember
 * @typedef {import('discord.js').Role} Role
 */

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('role')
  .setDescription('Add or remove roles from a member (HR & Admin only)')
  .addUserOption(option =>
    option
      .setName('user')
      .setDescription('The user to modify roles for')
      .setRequired(true)
  )
  .addStringOption(option =>
    option
      .setName('action')
      .setDescription('Whether to add or remove the role')
      .setRequired(true)
      .addChoices(
        { name: 'Add Role', value: 'add' },
        { name: 'Remove Role', value: 'remove' }
      )
  )
  .addRoleOption(option =>
    option
      .setName('role')
      .setDescription('The role to add or remove')
      .setRequired(true)
  );

/**
 * Execute the role management command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'role',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `You're being rate limited. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `role:${interaction.user.id}`;
    const roleCooldown = cooldown.getCooldownMs('role');
    if (!cooldown.isExpired(cooldownKey, roleCooldown)) {
      return await safeReply(interaction, {
        content: `⏳ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, roleCooldown) / 1000)}s before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get command options
    const targetUser = interaction.options.getUser('user', true);
    const action = interaction.options.getString('action', true);
    const role = interaction.options.getRole('role', true);

    // Validate guild context
    if (!interaction.guild || !interaction.member) {
      return await safeReply(interaction, {
        content: '❌ This command can only be used in a server.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get target member
    const targetMember = await interaction.guild.members
      .fetch(targetUser.id)
      .catch(() => null);
    if (!targetMember) {
      return await safeReply(interaction, {
        content: '❌ User not found in this server.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Permission checks
    /** @type {GuildMember} */
    const executorMember = /** @type {GuildMember} */ (interaction.member);

    const isAdmin = hasAdminPermission(executorMember);
    const isHR = hasHumanResourcesPermission(executorMember);

    if (!isAdmin && !isHR) {
      return await safeReply(interaction, {
        content:
          '🚫 **Access Denied!** You need HR or Admin permissions to use this command.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Check if bot has permission to manage roles
    if (
      !interaction.guild.members.me?.permissions.has(
        PermissionFlagsBits.ManageRoles
      )
    ) {
      return await safeReply(interaction, {
        content: "❌ I don't have permission to manage roles in this server.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Check role hierarchy - bot's highest role must be higher than the target role
    const botHighestRole = interaction.guild.members.me?.roles.highest;
    if (!botHighestRole || role.position >= botHighestRole.position) {
      return await safeReply(interaction, {
        content:
          "❌ I cannot manage this role because it's higher than or equal to my highest role.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Check executor hierarchy - executor's highest role must be higher than the target role
    const executorHighestRole = executorMember.roles.highest;
    if (
      role.position >= executorHighestRole.position &&
      !executorMember.permissions.has(PermissionFlagsBits.Administrator)
    ) {
      return await safeReply(interaction, {
        content:
          "❌ You cannot manage this role because it's higher than or equal to your highest role.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Prevent managing admin/HR roles unless executor is admin
    const protectedRoles = [botConfig.roles.admin, botConfig.roles.hr];
    if (protectedRoles.includes(role.id) && !isAdmin) {
      return await safeReply(interaction, {
        content: '❌ Only administrators can manage admin and HR roles.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Perform the role action
    let success = false;
    let actionText = '';

    if (action === 'add') {
      if (targetMember.roles.cache.has(role.id)) {
        return await safeReply(interaction, {
          content: `❌ ${targetUser.tag} already has the ${role.name} role.`,
          flags: [MessageFlags.Ephemeral],
        });
      }

      await targetMember.roles.add(role);
      success = true;
      actionText = 'added to';
    } else if (action === 'remove') {
      if (!targetMember.roles.cache.has(role.id)) {
        return await safeReply(interaction, {
          content: `❌ ${targetUser.tag} doesn't have the ${role.name} role.`,
          flags: [MessageFlags.Ephemeral],
        });
      }

      await targetMember.roles.remove(role);
      success = true;
      actionText = 'removed from';
    }

    if (success) {
      // Log the action
      logger.info(
        {
          event: 'role_management',
          command: 'role',
          executor: interaction.user.id,
          executorTag: interaction.user.tag,
          target: targetUser.id,
          targetTag: targetUser.tag,
          role: role.id,
          roleName: role.name,
          action: action,
          guild: interaction.guild.id,
        },
        `Role ${action} by ${interaction.user.tag}: ${role.name} ${actionText} ${targetUser.tag}`
      );

      // Set cooldown
      cooldown.set(cooldownKey);

      // Success response
      await safeReply(interaction, {
        content: `✅ Successfully ${actionText === 'added to' ? 'added' : 'removed'} the **${role.name}** role ${actionText} ${targetUser.tag}.`,
        flags: [MessageFlags.Ephemeral],
      });
    }
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'role' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content: '⚠️ **System Error!** Please contact an administrator.',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard(
    'role',
    CommandMiddleware.hrAndAdmin(_execute)
  ),
};
