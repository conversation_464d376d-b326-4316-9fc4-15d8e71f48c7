/**
 * @file RESTART.JS
 *
 * @version 3.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command to restart the D.I.S Bot via the launcher.
 * Sends a restart signal to the parent process for a clean restart.
 * Supports both graceful and forced restarts.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import {
  EmbedBuilder,
  MessageFlags,
  PermissionFlagsBits,
  SlashCommandBuilder,
} from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import logger from '../../utils/logger.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').Client} DiscordClient
 */

/**
 * @typedef {Object} GlobalThis
 * @property {(event: string, payload: any) => void} [sendIpcEvent] - IPC event sender function
 */

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('restart')
  .setDescription('Restart the D.I.S APP')
  .addBooleanOption(option =>
    option
      .setName('force')
      .setDescription(
        'Force an immediate restart without grace period - not recommended'
      )
      .setRequired(false)
  )
  .setDefaultMemberPermissions(PermissionFlagsBits.Administrator);

// ------------ MAIN EXECUTE FUNCTION
/**
 * Executes the restart command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction object
 * @param {DiscordClient} client - The Discord client instance
 * @returns {Promise<void>}
 */
async function _execute(interaction, client) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'restart',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `You're being rate limited. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const cooldownKey = `restart:${interaction.user.id}`;
    const restartCooldown = cooldown.getCooldownMs('restart');
    if (!cooldown.isExpired(cooldownKey, restartCooldown)) {
      return await safeReply(interaction, {
        content: `⏳ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, restartCooldown) / 1000)}s before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

    // Get force flag option
    const forceRestart = interaction.options.getBoolean('force') ?? false;

    // Create embed message to inform the user about the restart
    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.warning.replace(/^#/, ''), 16))
      .setTitle('🔄 Restarting')
      .setDescription(
        forceRestart
          ? 'Bot is now force restarting...'
          : 'Bot is preparing to restart gracefully...'
      )
      .setFooter({
        text: `Initiated by ${interaction.user.tag}`,
        iconURL: interaction.user.displayAvatarURL({ forceStatic: false }),
      })
      .setTimestamp();

    await safeReply(interaction, { embeds: [embed] });

    logger.info(
      {
        event: 'admin_restart',
        command: 'restart',
        user: interaction.user.id,
        username: interaction.user.tag,
        guild: interaction.guild?.id,
        options: forceRestart ? 'forced' : 'graceful',
      },
      `Admin-initiated restart by ${interaction.user.tag} (${interaction.user.id})${forceRestart ? ' (forced)' : ''}`
    );

    // Use IPC if available, else fallback to process.exit
    /** @type {GlobalThis} */
    const globalThisTyped = /** @type {GlobalThis} */ (globalThis);
    if (typeof globalThisTyped.sendIpcEvent === 'function') {
      globalThisTyped.sendIpcEvent('restart', {
        forced: forceRestart,
        initiator: {
          id: interaction.user.id,
          tag: interaction.user.tag,
        },
        timestamp: Date.now(),
      });
      logger.info(
        {
          event: 'admin_restart',
          command: 'restart',
          user: interaction.user.id,
          username: interaction.user.tag,
          guild: interaction.guild?.id,
          options: forceRestart ? 'forced' : 'graceful',
        },
        'Restart signal sent to launcher via IPC'
      );
      // For graceful restart, launcher will restart the bot process.
      // For forced restart, exit after a short delay.
      if (forceRestart) {
        setTimeout(() => {
          logger.info(
            {
              event: 'admin_restart',
              command: 'restart',
              user: interaction.user.id,
              username: interaction.user.tag,
              guild: interaction.guild?.id,
              options: forceRestart ? 'forced' : 'graceful',
            },
            'Force exiting for immediate restart'
          );
          process.exit(0);
        }, 1000);
      }
    } else {
      logger.warn(
        {
          event: 'admin_restart',
          command: 'restart',
          user: interaction.user.id,
          username: interaction.user.tag,
          guild: interaction.guild?.id,
          options: forceRestart ? 'forced' : 'graceful',
        },
        'No IPC available, exiting directly'
      );
      setTimeout(() => process.exit(0), 1000);
    }

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'restart' },
      client,
      {
        critical: true,
      }
    );
    await safeReply(interaction, {
      content: '⚠️ An error occurred while processing this command.',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.admin('restart', _execute),
};
