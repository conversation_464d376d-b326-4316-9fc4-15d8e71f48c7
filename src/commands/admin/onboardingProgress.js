/**
 * @file ONBOARDING PROGRESS.JS
 *
 * @version 1.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command to view onboarding progress for all new co-workers.
 * Allows administrators and H<PERSON> to check the status of user onboarding.
 * Includes input validation, rate limiting, and robust error handling.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { messages } from '../../config/messages.js';
import { CommandMiddleware } from '../../middleware/commandAuthentication.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import db from '../../utils/Database.js';
import logger from '../../utils/logger.js';
import { createPaginatedMessage } from '../../utils/pagination.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').Client} DiscordClient
 * @typedef {import('discord.js').User} DiscordUser
 * @typedef {import('discord.js').GuildMember} GuildMember
 */

// ------------ CONSTANTS
const PAGE_SIZE = 10;

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('onboarding')
  .setDescription('View onboarding progress for all new co-workers.');

// ------------ MAIN COMMAND EXECUTION
/**
 * @param {ChatInputCommandInteraction} interaction
 */
async function _execute(interaction) {
  try {
    // Rate limit check for onboarding command
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'onboarding',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `You're being rate limited. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const cooldownKey = `onboarding:${interaction.user.id}`;
    const onboardingCooldown = cooldown.getCooldownMs('onboarding');
    if (!cooldown.isExpired(cooldownKey, onboardingCooldown)) {
      return await safeReply(interaction, {
        content: `⏳ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, onboardingCooldown) / 1000)}s before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

    // Fetch all users from the database
    const rows = await db.query(
      'SELECT id_dis_internal_workers, tag, onboardingStatus, verified, onboardingStartTime, verifiedAt FROM dis_internal_workers ORDER BY onboardingStatus DESC, verified DESC, onboardingStartTime DESC'
    );

    if (!rows.length) {
      return await safeReply(interaction, {
        content: 'No users found in the onboarding system.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Paginate if needed
    const pages = [];
    for (let i = 0; i < rows.length; i += PAGE_SIZE) {
      const pageRows = rows.slice(i, i + PAGE_SIZE);
      const embed = new EmbedBuilder()
        .setTitle('Onboarding Progress')
        .setColor('#ccf3ff')
        .setDescription('Current onboarding status for all users.')
        .setFooter({
          text: `Page ${Math.floor(i / PAGE_SIZE) + 1} of ${Math.ceil(rows.length / PAGE_SIZE)}`,
        })
        .setTimestamp();

      pageRows.forEach((user, idx) => {
        const status = user.verified
          ? '✅ Verified'
          : user.onboardingStatus === 'completed'
            ? '✅ Completed'
            : user.onboardingStatus === 'pending'
              ? '⏳ Pending'
              : user.onboardingStatus === 'cancelled'
                ? '❌ Cancelled'
                : '❔ Unknown';
        const started = user.onboardingStartTime
          ? `<t:${Math.floor(new Date(user.onboardingStartTime).getTime() / 1000)}:R>`
          : 'N/A';
        const verifiedAt = user.verifiedAt
          ? `<t:${Math.floor(new Date(user.verifiedAt).getTime() / 1000)}:R>`
          : 'N/A';
        embed.addFields({
          name: `${idx + 1 + i}. ${user.tag || user.id_dis_internal_workers}`,
          value: `ID: \`${user.id_dis_internal_workers}\`\nStatus: **${status}**\nStarted: ${started}\nVerified: ${verifiedAt}`,
          inline: false,
        });
      });
      pages.push(embed);
    }

    // Send paginated message
    await createPaginatedMessage(pages, interaction, {
      ephemeral: true,
    });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    logger.error(
      'Failed to fetch onboarding progress:',
      error instanceof Error ? error : new Error(String(error))
    );
    await safeReply(interaction, {
      content: messages.errors.internalError,
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ COMMAND SECURITY
export const execute = CommandHandler.standard(
  'onboarding',
  CommandMiddleware.hrAndAdmin(_execute)
);

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard(
    'onboarding',
    CommandMiddleware.hrAndAdmin(_execute)
  ),
  manualCooldown: true,
};
