/**
 * @file COFFEE.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Fun slash command that serves virtual coffee to users.
 * Provides random coffee types and brewing methods with engaging responses.
 * Includes rate limiting and cooldown management for spam prevention.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 */

// ------------ COFFEE DATA
const coffeeTypes = [
  'Espresso ☕',
  'Cappuccino ☕',
  'Latte ☕',
  'Americano ☕',
  'Mocha ☕',
  'Macchiato ☕',
  'Flat White ☕',
  'Cold Brew ❄️',
  'Frappé ❄️',
  'Turkish Coffee ☕',
  'French Press ☕',
  'Pour Over ☕',
];

const brewingMethods = [
  'carefully brewed with love',
  'expertly crafted by our virtual barista',
  'made with premium beans from the cloud',
  'prepared with the finest digital ingredients',
  'brewed to perfection in our cyber café',
  'made with a touch of bot magic',
  'crafted with algorithmic precision',
  'prepared with extra care and attention',
];

const coffeeMessages = [
  "Here's your perfect cup of coffee! ☕",
  'Fresh coffee, coming right up! ☕',
  'Your coffee is ready to energize your day! ⚡',
  'Enjoy this delicious brew! ☕',
  'A warm cup of happiness for you! ☕',
  'Coffee time! Perfect for coding sessions! 💻',
  'Your daily dose of caffeine is served! ☕',
  'Freshly brewed just for you! ☕',
];

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('coffee')
  .setDescription('Get a virtual cup of coffee to energize your day! ☕');

/**
 * Execute the coffee command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'coffee',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `☕ Slow down there, coffee lover! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `coffee:${interaction.user.id}`;
    const coffeeCooldown = cooldown.getCooldownMs('coffee');
    if (!cooldown.check(cooldownKey, coffeeCooldown)) {
      return await safeReply(interaction, {
        content: `☕ Your coffee is still brewing! Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, coffeeCooldown) / 1000)}s before ordering another cup.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Random selections
    const selectedCoffee =
      coffeeTypes[Math.floor(Math.random() * coffeeTypes.length)];
    const selectedMethod =
      brewingMethods[Math.floor(Math.random() * brewingMethods.length)];
    const selectedMessage =
      coffeeMessages[Math.floor(Math.random() * coffeeMessages.length)];

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.primary.replace(/^#/, ''), 16))
      .setTitle('☕ Virtual Coffee Shop')
      .setDescription(selectedMessage)
      .addFields(
        {
          name: '🫘 Your Order',
          value: selectedCoffee,
          inline: true,
        },
        {
          name: '⚙️ Brewing Method',
          value: selectedMethod,
          inline: true,
        },
        {
          name: '👤 Served To',
          value: interaction.user.toString(),
          inline: true,
        }
      )
      .setFooter({
        text: `Dynamic Innovative Studio • ${botConfig.version}`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'coffee' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content:
        '☕ Oops! Our coffee machine is having issues. Please try again later!',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('coffee', _execute, {
    manualCooldown: true,
  }),
};
