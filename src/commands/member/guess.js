/**
 * @file GUESS.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Fun slash command that creates a number guessing game for users.
 * Players guess a number within a specified range and receive feedback.
 * Includes rate limiting and cooldown management for spam prevention.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 */

// ------------ GAME UTILITIES
/**
 * Generate a random number between min and max (inclusive).
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {number} - Random number
 */
function generateRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Get feedback message based on how close the guess is.
 * @param {number} guess - User's guess
 * @param {number} target - Target number
 * @param {number} range - Total range of possible numbers
 * @returns {string} - Feedback message
 */
function getFeedback(guess, target, range) {
  const difference = Math.abs(guess - target);
  const percentageOff = (difference / range) * 100;

  if (difference === 0) {
    return '🎯 **PERFECT!** You got it exactly right!';
  } else if (percentageOff <= 5) {
    return "🔥 **SO CLOSE!** You're burning hot!";
  } else if (percentageOff <= 15) {
    return "🌡️ **WARM!** You're getting close!";
  } else if (percentageOff <= 30) {
    return "❄️ **COOL!** You're in the right neighborhood!";
  } else if (percentageOff <= 50) {
    return "🧊 **COLD!** You're a bit far off!";
  } else {
    return "🥶 **FREEZING!** You're way off!";
  }
}

/**
 * Get color based on accuracy.
 * @param {number} guess - User's guess
 * @param {number} target - Target number
 * @param {number} range - Total range of possible numbers
 * @returns {string} - Hex color code
 */
function getAccuracyColor(guess, target, range) {
  const difference = Math.abs(guess - target);
  const percentageOff = (difference / range) * 100;

  if (difference === 0) return '#ffd700'; // Gold for perfect
  if (percentageOff <= 5) return '#ff4500'; // Red-orange for very close
  if (percentageOff <= 15) return '#ff8c00'; // Orange for close
  if (percentageOff <= 30) return '#ffa500'; // Light orange for warm
  if (percentageOff <= 50) return '#87ceeb'; // Sky blue for cool
  return '#4169e1'; // Royal blue for cold
}

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('guess')
  .setDescription('Play a number guessing game! 🎯')
  .addIntegerOption(option =>
    option
      .setName('number')
      .setDescription('Your guess (1-100)')
      .setRequired(true)
      .setMinValue(1)
      .setMaxValue(100)
  )
  .addIntegerOption(option =>
    option
      .setName('max')
      .setDescription('Maximum number for the range (default: 100)')
      .setRequired(false)
      .setMinValue(10)
      .setMaxValue(1000)
  );

/**
 * Execute the guess command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'guess',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `🎯 The number generator is recharging! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `guess:${interaction.user.id}`;
    const guessCooldown = cooldown.getCooldownMs('guess');
    if (!cooldown.check(cooldownKey, guessCooldown)) {
      return await safeReply(interaction, {
        content: `🎯 Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, guessCooldown) / 1000)}s before making another guess.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get options
    const userGuess = interaction.options.getInteger('number', true);
    const maxNumber = interaction.options.getInteger('max') ?? 100;

    // Validate guess is within range
    if (userGuess > maxNumber) {
      return await safeReply(interaction, {
        content: `🎯 Your guess (${userGuess}) is higher than the maximum allowed (${maxNumber})!`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Generate target number
    const targetNumber = generateRandomNumber(1, maxNumber);
    const range = maxNumber;

    // Calculate results
    const difference = Math.abs(userGuess - targetNumber);
    const accuracy = Math.max(0, 100 - Math.round((difference / range) * 100));
    const feedback = getFeedback(userGuess, targetNumber, range);
    const embedColor = getAccuracyColor(userGuess, targetNumber, range);

    // Determine if it's a win
    const isWin = userGuess === targetNumber;

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(embedColor.replace(/^#/, ''), 16))
      .setTitle('🎯 Number Guessing Game')
      .setDescription("Let's see how close you got!")
      .addFields(
        {
          name: '🎲 Your Guess',
          value: `**${userGuess}**`,
          inline: true,
        },
        {
          name: '🎯 Target Number',
          value: `**${targetNumber}**`,
          inline: true,
        },
        {
          name: '📏 Range',
          value: `1 - ${maxNumber}`,
          inline: true,
        },
        {
          name: '📊 Accuracy',
          value: `**${accuracy}%**`,
          inline: true,
        },
        {
          name: '📐 Difference',
          value: `**${difference}**`,
          inline: true,
        },
        {
          name: '👤 Player',
          value: interaction.user.toString(),
          inline: true,
        },
        {
          name: '💭 Feedback',
          value: feedback,
          inline: false,
        }
      )
      .setFooter({
        text: `Dynamic Innovative Studio • ${botConfig.version}`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    // Add special message for perfect guess
    if (isWin) {
      embed.addFields({
        name: '🏆 WINNER!',
        value: 'Congratulations! You guessed the exact number!',
        inline: false,
      });
    }

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'guess' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content:
        '🎯 The guessing game encountered an error. Please try again later!',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('guess', _execute, {
    manualCooldown: true,
  }),
};
