/**
 * @file COMPLIMENT.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Fun slash command that gives users random compliments to brighten their day.
 * Features a variety of positive messages with engaging presentation.
 * Includes rate limiting and cooldown management for spam prevention.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 */

// ------------ COMPLIMENT DATA
const compliments = [
  {
    text: 'You have an amazing ability to make others feel comfortable and welcome!',
    emoji: '🌟',
    category: 'Social',
  },
  {
    text: 'Your creativity and innovative thinking always impress everyone around you!',
    emoji: '🎨',
    category: 'Creative',
  },
  {
    text: 'You have such a positive energy that lights up any room you enter!',
    emoji: '✨',
    category: 'Energy',
  },
  {
    text: 'Your dedication and hard work are truly inspiring to witness!',
    emoji: '💪',
    category: 'Work Ethic',
  },
  {
    text: 'You have an incredible talent for solving problems with elegance!',
    emoji: '🧠',
    category: 'Intelligence',
  },
  {
    text: 'Your kindness and compassion make the world a better place!',
    emoji: '❤️',
    category: 'Kindness',
  },
  {
    text: "You have such a great sense of humor that always brightens everyone's day!",
    emoji: '😄',
    category: 'Humor',
  },
  {
    text: 'Your determination and perseverance are absolutely admirable!',
    emoji: '🚀',
    category: 'Determination',
  },
  {
    text: 'You have an amazing ability to see the best in people and situations!',
    emoji: '🌈',
    category: 'Optimism',
  },
  {
    text: 'Your unique perspective and insights always add value to any discussion!',
    emoji: '💡',
    category: 'Wisdom',
  },
  {
    text: 'You have such excellent communication skills that make complex things simple!',
    emoji: '🗣️',
    category: 'Communication',
  },
  {
    text: 'Your reliability and trustworthiness make you an incredible person to know!',
    emoji: '🤝',
    category: 'Trust',
  },
  {
    text: 'You have an extraordinary ability to learn and adapt to new challenges!',
    emoji: '📚',
    category: 'Learning',
  },
  {
    text: 'Your leadership qualities inspire others to be their best selves!',
    emoji: '👑',
    category: 'Leadership',
  },
  {
    text: 'You have such a wonderful way of making everyone feel valued and heard!',
    emoji: '🎯',
    category: 'Empathy',
  },
];

const complimentIntros = [
  "Here's something wonderful about you:",
  'Time for a well-deserved compliment:',
  'You deserve to hear this:',
  'A little positivity coming your way:',
  'Something amazing about you:',
  'You should know that:',
];

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('compliment')
  .setDescription('Receive a heartfelt compliment to brighten your day! ✨');

/**
 * Execute the compliment command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'compliment',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `✨ You're so amazing that even the compliment generator needs a break! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `compliment:${interaction.user.id}`;
    const complimentCooldown = cooldown.getCooldownMs('compliment');
    if (!cooldown.check(cooldownKey, complimentCooldown)) {
      return await safeReply(interaction, {
        content: `✨ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, complimentCooldown) / 1000)}s before receiving another compliment. You're already amazing!`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Random selections
    const selectedCompliment =
      compliments[Math.floor(Math.random() * compliments.length)];
    const selectedIntro =
      complimentIntros[Math.floor(Math.random() * complimentIntros.length)];

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.primary.replace(/^#/, ''), 16))
      .setTitle(`${selectedCompliment.emoji} Daily Compliment`)
      .setDescription(selectedIntro)
      .addFields(
        {
          name: '💝 Your Compliment',
          value: selectedCompliment.text,
          inline: false,
        },
        {
          name: '🏷️ Category',
          value: selectedCompliment.category,
          inline: true,
        },
        {
          name: '👤 For',
          value: interaction.user.toString(),
          inline: true,
        },
        {
          name: '💫 Remember',
          value: 'You are valued, appreciated, and amazing just as you are!',
          inline: false,
        }
      )
      .setFooter({
        text: `Dynamic Innovative Studio • ${botConfig.version} • Spread positivity!`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'compliment' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content:
        "✨ The compliment generator is having a moment, but you're still amazing! Please try again later!",
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('compliment', _execute, {
    manualCooldown: true,
  }),
};
