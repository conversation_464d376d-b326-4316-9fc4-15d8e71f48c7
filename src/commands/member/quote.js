/**
 * @file QUOTE.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Fun slash command that provides inspirational and motivational quotes.
 * Features a curated collection of quotes from various categories with proper attribution.
 * Includes rate limiting and cooldown management for spam prevention.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 */

// ------------ QUOTE DATA
const quotes = [
  {
    text: 'The only way to do great work is to love what you do.',
    author: 'Steve Jobs',
    category: 'Work & Passion',
    emoji: '💼',
  },
  {
    text: 'Innovation distinguishes between a leader and a follower.',
    author: 'Steve Jobs',
    category: 'Innovation',
    emoji: '💡',
  },
  {
    text: 'The future belongs to those who believe in the beauty of their dreams.',
    author: 'Eleanor Roosevelt',
    category: 'Dreams',
    emoji: '🌟',
  },
  {
    text: 'It is during our darkest moments that we must focus to see the light.',
    author: 'Aristotle',
    category: 'Perseverance',
    emoji: '🕯️',
  },
  {
    text: 'Success is not final, failure is not fatal: it is the courage to continue that counts.',
    author: 'Winston Churchill',
    category: 'Courage',
    emoji: '🦁',
  },
  {
    text: 'The only impossible journey is the one you never begin.',
    author: 'Tony Robbins',
    category: 'Beginning',
    emoji: '🚀',
  },
  {
    text: "Your limitation—it's only your imagination.",
    author: 'Unknown',
    category: 'Mindset',
    emoji: '🧠',
  },
  {
    text: 'Great things never come from comfort zones.',
    author: 'Unknown',
    category: 'Growth',
    emoji: '🌱',
  },
  {
    text: 'Dream it. Wish it. Do it.',
    author: 'Unknown',
    category: 'Action',
    emoji: '✨',
  },
  {
    text: "Don't wait for opportunity. Create it.",
    author: 'Unknown',
    category: 'Opportunity',
    emoji: '🔨',
  },
  {
    text: 'The way to get started is to quit talking and begin doing.',
    author: 'Walt Disney',
    category: 'Action',
    emoji: '🎬',
  },
  {
    text: "Life is what happens to you while you're busy making other plans.",
    author: 'John Lennon',
    category: 'Life',
    emoji: '🎵',
  },
  {
    text: 'The future depends on what you do today.',
    author: 'Mahatma Gandhi',
    category: 'Present',
    emoji: '⏰',
  },
  {
    text: 'It is never too late to be what you might have been.',
    author: 'George Eliot',
    category: 'Potential',
    emoji: '🦋',
  },
  {
    text: "Code is like humor. When you have to explain it, it's bad.",
    author: 'Cory House',
    category: 'Programming',
    emoji: '💻',
  },
  {
    text: 'First, solve the problem. Then, write the code.',
    author: 'John Johnson',
    category: 'Programming',
    emoji: '🔧',
  },
  {
    text: 'The best error message is the one that never shows up.',
    author: 'Thomas Fuchs',
    category: 'Programming',
    emoji: '🐛',
  },
  {
    text: 'Simplicity is the ultimate sophistication.',
    author: 'Leonardo da Vinci',
    category: 'Simplicity',
    emoji: '🎨',
  },
];

const quoteIntros = [
  "Here's some wisdom for you:",
  'A thought to inspire your day:',
  'Words of wisdom:',
  'Something to ponder:',
  'A quote to motivate you:',
  'Inspiration incoming:',
];

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('quote')
  .setDescription('Get an inspirational quote to motivate your day! 📜');

/**
 * Execute the quote command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'quote',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `📜 The wisdom library is recharging! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `quote:${interaction.user.id}`;
    const quoteCooldown = cooldown.getCooldownMs('quote');
    if (!cooldown.check(cooldownKey, quoteCooldown)) {
      return await safeReply(interaction, {
        content: `📜 Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, quoteCooldown) / 1000)}s before requesting another quote.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Random selections
    const selectedQuote = quotes[Math.floor(Math.random() * quotes.length)];
    const selectedIntro =
      quoteIntros[Math.floor(Math.random() * quoteIntros.length)];

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.primary.replace(/^#/, ''), 16))
      .setTitle(`${selectedQuote.emoji} Daily Quote`)
      .setDescription(selectedIntro)
      .addFields(
        {
          name: '💬 Quote',
          value: `*"${selectedQuote.text}"*`,
          inline: false,
        },
        {
          name: '👤 Author',
          value: selectedQuote.author,
          inline: true,
        },
        {
          name: '🏷️ Category',
          value: selectedQuote.category,
          inline: true,
        },
        {
          name: '📖 Shared With',
          value: interaction.user.toString(),
          inline: true,
        }
      )
      .setFooter({
        text: `Dynamic Innovative Studio • ${botConfig.version} • Stay inspired!`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'quote' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content:
        '📜 The quote library is temporarily unavailable. Please try again later!',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('quote', _execute, {
    manualCooldown: true,
  }),
};
