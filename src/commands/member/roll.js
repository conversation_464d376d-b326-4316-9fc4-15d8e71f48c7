/**
 * @file ROLL.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Fun slash command that rolls dice with customizable sides and quantity.
 * Supports standard dice notation and provides detailed roll results.
 * Includes rate limiting and cooldown management for spam prevention.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 */

// ------------ DICE UTILITIES
/**
 * Roll a single die with specified sides.
 * @param {number} sides - Number of sides on the die
 * @returns {number} - Random number between 1 and sides
 */
function rollDie(sides) {
  return Math.floor(Math.random() * sides) + 1;
}

/**
 * Get emoji for dice result based on value and sides.
 * @param {number} value - The rolled value
 * @param {number} sides - Number of sides on the die
 * @returns {string} - Appropriate emoji
 */
function getDiceEmoji(value, sides) {
  if (sides === 6) {
    const diceEmojis = ['⚀', '⚁', '⚂', '⚃', '⚄', '⚅'];
    return diceEmojis[value - 1] || '🎲';
  }

  if (value === 1) return '💀'; // Critical fail
  if (value === sides) return '✨'; // Critical success
  if (value >= sides * 0.8) return '🔥'; // High roll
  if (value <= sides * 0.2) return '😅'; // Low roll
  return '🎲'; // Normal roll
}

/**
 * Get color based on roll quality.
 * @param {number} value - The rolled value
 * @param {number} sides - Number of sides on the die
 * @returns {string} - Hex color code
 */
function getRollColor(value, sides) {
  if (value === 1) return '#ff0000'; // Critical fail - red
  if (value === sides) return '#ffd700'; // Critical success - gold
  if (value >= sides * 0.8) return '#00ff00'; // High roll - green
  if (value <= sides * 0.2) return '#ff6600'; // Low roll - orange
  return '#0099ff'; // Normal roll - blue
}

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('roll')
  .setDescription('Roll dice with customizable sides and quantity! 🎲')
  .addIntegerOption(option =>
    option
      .setName('sides')
      .setDescription('Number of sides on the die (default: 6)')
      .setRequired(false)
      .setMinValue(2)
      .setMaxValue(100)
  )
  .addIntegerOption(option =>
    option
      .setName('count')
      .setDescription('Number of dice to roll (default: 1)')
      .setRequired(false)
      .setMinValue(1)
      .setMaxValue(10)
  );

/**
 * Execute the roll command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'roll',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `🎲 The dice are still rolling! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `roll:${interaction.user.id}`;
    const rollCooldown = cooldown.getCooldownMs('roll');
    if (!cooldown.check(cooldownKey, rollCooldown)) {
      return await safeReply(interaction, {
        content: `🎲 Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, rollCooldown) / 1000)}s before rolling again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get options with defaults
    const sides = interaction.options.getInteger('sides') ?? 6;
    const count = interaction.options.getInteger('count') ?? 1;

    // Roll the dice
    const rolls = [];
    let total = 0;

    for (let i = 0; i < count; i++) {
      const roll = rollDie(sides);
      rolls.push(roll);
      total += roll;
    }

    // Determine the primary color based on the highest roll
    const maxRoll = Math.max(...rolls);
    const embedColor = getRollColor(maxRoll, sides);

    // Create results display
    const rollsDisplay = rolls
      .map(roll => {
        const emoji = getDiceEmoji(roll, sides);
        return `${emoji} **${roll}**`;
      })
      .join(' ');

    // Calculate statistics
    const average = (total / count).toFixed(1);
    const minPossible = count;
    const maxPossible = count * sides;

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(embedColor.replace(/^#/, ''), 16))
      .setTitle('🎲 Dice Roll Results')
      .setDescription(`Rolling ${count}d${sides}...`)
      .addFields(
        {
          name: '🎯 Results',
          value: rollsDisplay,
          inline: false,
        },
        {
          name: '📊 Total',
          value: `**${total}**`,
          inline: true,
        },
        {
          name: '📈 Average',
          value: `**${average}**`,
          inline: true,
        },
        {
          name: '📏 Range',
          value: `${minPossible} - ${maxPossible}`,
          inline: true,
        },
        {
          name: '👤 Rolled By',
          value: interaction.user.toString(),
          inline: true,
        },
        {
          name: '🎲 Dice Type',
          value: `${count}d${sides}`,
          inline: true,
        }
      )
      .setFooter({
        text: `Dynamic Innovative Studio • ${botConfig.version}`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    // Add special messages for notable rolls
    if (rolls.some(roll => roll === 1)) {
      embed.addFields({
        name: '💀 Critical Fail!',
        value: 'Ouch! At least one die rolled a 1!',
        inline: false,
      });
    } else if (rolls.some(roll => roll === sides)) {
      embed.addFields({
        name: '✨ Critical Success!',
        value: 'Amazing! At least one die rolled the maximum!',
        inline: false,
      });
    }

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'roll' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content: '🎲 The dice got stuck! Please try again later!',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('roll', _execute, {
    manualCooldown: true,
  }),
};
