/**
 * @file 8BALL.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Fun slash command that provides Magic 8-Ball style answers to user questions.
 * Includes classic 8-ball responses with engaging presentation and proper error handling.
 * Features rate limiting and cooldown management for spam prevention.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 */

// ------------ 8-BALL RESPONSES
const eightBallResponses = [
  // Positive responses
  { text: 'It is certain.', type: 'positive', color: '#00ff00' },
  { text: 'It is decidedly so.', type: 'positive', color: '#00ff00' },
  { text: 'Without a doubt.', type: 'positive', color: '#00ff00' },
  { text: 'Yes definitely.', type: 'positive', color: '#00ff00' },
  { text: 'You may rely on it.', type: 'positive', color: '#00ff00' },
  { text: 'As I see it, yes.', type: 'positive', color: '#00ff00' },
  { text: 'Most likely.', type: 'positive', color: '#00ff00' },
  { text: 'Outlook good.', type: 'positive', color: '#00ff00' },
  { text: 'Yes.', type: 'positive', color: '#00ff00' },
  { text: 'Signs point to yes.', type: 'positive', color: '#00ff00' },

  // Neutral/uncertain responses
  { text: 'Reply hazy, try again.', type: 'neutral', color: '#ffaa00' },
  { text: 'Ask again later.', type: 'neutral', color: '#ffaa00' },
  { text: 'Better not tell you now.', type: 'neutral', color: '#ffaa00' },
  { text: 'Cannot predict now.', type: 'neutral', color: '#ffaa00' },
  { text: 'Concentrate and ask again.', type: 'neutral', color: '#ffaa00' },

  // Negative responses
  { text: "Don't count on it.", type: 'negative', color: '#ff0000' },
  { text: 'My reply is no.', type: 'negative', color: '#ff0000' },
  { text: 'My sources say no.', type: 'negative', color: '#ff0000' },
  { text: 'Outlook not so good.', type: 'negative', color: '#ff0000' },
  { text: 'Very doubtful.', type: 'negative', color: '#ff0000' },
];

const responseEmojis = {
  positive: '✅',
  neutral: '🤔',
  negative: '❌',
};

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('8ball')
  .setDescription(
    'Ask the Magic 8-Ball a question and receive mystical wisdom! 🎱'
  )
  .addStringOption(option =>
    option
      .setName('question')
      .setDescription('Your question for the Magic 8-Ball')
      .setRequired(true)
      .setMaxLength(200)
  );

/**
 * Execute the 8ball command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: '8ball',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `🎱 The Magic 8-Ball is recharging its mystical powers! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `8ball:${interaction.user.id}`;
    const ballCooldown = cooldown.getCooldownMs('8ball');
    if (!cooldown.check(cooldownKey, ballCooldown)) {
      return await safeReply(interaction, {
        content: `🎱 The Magic 8-Ball is still swirling! Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, ballCooldown) / 1000)}s before asking another question.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get the question
    const question = interaction.options.getString('question', true);

    // Validate question (basic check for question-like format)
    if (!question.trim()) {
      return await safeReply(interaction, {
        content: '🎱 The Magic 8-Ball needs a proper question to answer!',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Random response selection
    const selectedResponse =
      eightBallResponses[Math.floor(Math.random() * eightBallResponses.length)];
    const emoji = responseEmojis[selectedResponse.type];

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(selectedResponse.color.replace(/^#/, ''), 16))
      .setTitle('🎱 Magic 8-Ball')
      .setDescription('*The mystical sphere swirls with ancient wisdom...*')
      .addFields(
        {
          name: '❓ Your Question',
          value: `"${question}"`,
          inline: false,
        },
        {
          name: `${emoji} The 8-Ball Says`,
          value: `**${selectedResponse.text}**`,
          inline: false,
        },
        {
          name: '👤 Asked By',
          value: interaction.user.toString(),
          inline: true,
        },
        {
          name: '🔮 Response Type',
          value:
            selectedResponse.type.charAt(0).toUpperCase() +
            selectedResponse.type.slice(1),
          inline: true,
        }
      )
      .setFooter({
        text: `Dynamic Innovative Studio • ${botConfig.version}`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: '8ball' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content:
        '🎱 The Magic 8-Ball has encountered a mystical error. Please try again later!',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('8ball', _execute, {
    manualCooldown: true,
  }),
};
