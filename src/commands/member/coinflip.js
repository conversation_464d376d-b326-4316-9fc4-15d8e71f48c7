/**
 * @file COINFLIP.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Fun slash command that flips a virtual coin with heads or tails results.
 * Provides engaging animations and results with proper error handling.
 * Includes rate limiting and cooldown management for spam prevention.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 */

// ------------ COIN FLIP DATA
const coinResults = [
  {
    result: 'Heads',
    emoji: '🪙',
    description: 'The coin landed heads up!',
    color: '#ffd700',
  },
  {
    result: 'Tails',
    emoji: '🔘',
    description: 'The coin landed tails up!',
    color: '#c0c0c0',
  },
];

const flipMessages = [
  'Flipping the coin...',
  'The coin spins through the air...',
  'Watch it tumble and turn...',
  'Gravity does its work...',
  'The coin dances in the air...',
  'Round and round it goes...',
];

const resultMessages = [
  'And the result is...',
  'The coin has decided...',
  'Fate has spoken...',
  'The universe has chosen...',
  'The answer is revealed...',
  'The coin settles on...',
];

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('coinflip')
  .setDescription(
    'Flip a virtual coin and see if it lands on heads or tails! 🪙'
  );

/**
 * Execute the coinflip command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'coinflip',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `🪙 The coin is still in the air! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `coinflip:${interaction.user.id}`;
    const flipCooldown = cooldown.getCooldownMs('coinflip');
    if (!cooldown.check(cooldownKey, flipCooldown)) {
      return await safeReply(interaction, {
        content: `🪙 Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, flipCooldown) / 1000)}s before flipping another coin.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Random selections
    const selectedResult =
      coinResults[Math.floor(Math.random() * coinResults.length)];
    const selectedFlipMessage =
      flipMessages[Math.floor(Math.random() * flipMessages.length)];
    const selectedResultMessage =
      resultMessages[Math.floor(Math.random() * resultMessages.length)];

    // Generate a random flip count for fun
    const flipCount = Math.floor(Math.random() * 5) + 3; // 3-7 flips

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(selectedResult.color.replace(/^#/, ''), 16))
      .setTitle('🪙 Coin Flip')
      .setDescription(selectedFlipMessage)
      .addFields(
        {
          name: '🎯 Result',
          value: `${selectedResult.emoji} **${selectedResult.result}**`,
          inline: true,
        },
        {
          name: '🔄 Flips',
          value: `${flipCount}`,
          inline: true,
        },
        {
          name: '👤 Flipped By',
          value: interaction.user.toString(),
          inline: true,
        },
        {
          name: '💭 Outcome',
          value: selectedResult.description,
          inline: false,
        },
        {
          name: '📊 Probability',
          value: 'Each side had a 50% chance',
          inline: false,
        }
      )
      .setFooter({
        text: `Dynamic Innovative Studio • ${botConfig.version}`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    // Add result message
    embed.setDescription(
      `${selectedFlipMessage}\n\n*${selectedResultMessage}*`
    );

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'coinflip' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content: '🪙 The coin got lost in the void! Please try again later!',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('coinflip', _execute, {
    manualCooldown: true,
  }),
};
