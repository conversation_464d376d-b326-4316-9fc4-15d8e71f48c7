/**
 * @file VIBECHECK.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Fun slash command that checks the user's current vibe with random responses.
 * Provides entertaining vibe assessments with emojis and engaging messages.
 * Includes rate limiting and cooldown management for spam prevention.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 */

// ------------ VIBE DATA
const vibeResults = [
  {
    vibe: 'Absolutely Immaculate ✨',
    description:
      "Your vibe is off the charts! You're radiating pure positive energy!",
    color: '#00ff00',
    emoji: '✨',
  },
  {
    vibe: 'Pretty Solid 😎',
    description: "You're feeling good and it shows! Keep that energy flowing!",
    color: '#00aa00',
    emoji: '😎',
  },
  {
    vibe: 'Chill Vibes 😌',
    description: "You're in a relaxed state of mind. Very zen, very cool.",
    color: '#0088ff',
    emoji: '😌',
  },
  {
    vibe: 'Mysterious Energy 🌙',
    description: "There's something enigmatic about your aura today...",
    color: '#6600cc',
    emoji: '🌙',
  },
  {
    vibe: 'Chaotic Good 🔥',
    description: 'Your energy is wild but in the best possible way!',
    color: '#ff6600',
    emoji: '🔥',
  },
  {
    vibe: 'Coffee-Powered ☕',
    description: "You're running on pure caffeine and determination!",
    color: '#8b4513',
    emoji: '☕',
  },
  {
    vibe: 'Main Character Energy 👑',
    description: "You're absolutely owning today! The spotlight is yours!",
    color: '#ffd700',
    emoji: '👑',
  },
  {
    vibe: 'Sleepy but Cute 😴',
    description: "You might be tired, but you're still adorable!",
    color: '#9999ff',
    emoji: '😴',
  },
  {
    vibe: 'Coding Mode Activated 💻',
    description: "You're in the zone! Ready to debug the world!",
    color: '#00ff88',
    emoji: '💻',
  },
  {
    vibe: 'Unstoppable Force 💪',
    description: "Nothing can slow you down today! You're a powerhouse!",
    color: '#ff0066',
    emoji: '💪',
  },
];

const vibeIntros = [
  'Scanning your aura...',
  'Analyzing your energy levels...',
  'Checking the vibe-o-meter...',
  'Consulting the cosmic forces...',
  'Reading your digital aura...',
  'Calibrating the mood sensors...',
];

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('vibecheck')
  .setDescription('Check your current vibe and energy levels! ✨');

/**
 * Execute the vibecheck command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'vibecheck',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `✨ Whoa there! The vibe-o-meter needs to cool down. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `vibecheck:${interaction.user.id}`;
    const vibeCooldown = cooldown.getCooldownMs('vibecheck');
    if (!cooldown.check(cooldownKey, vibeCooldown)) {
      return await safeReply(interaction, {
        content: `✨ Your vibe is still being processed! Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, vibeCooldown) / 1000)}s before checking again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Random selections
    const selectedVibe =
      vibeResults[Math.floor(Math.random() * vibeResults.length)];
    const selectedIntro =
      vibeIntros[Math.floor(Math.random() * vibeIntros.length)];

    // Generate a random vibe percentage
    const vibePercentage = Math.floor(Math.random() * 41) + 60; // 60-100%

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(selectedVibe.color.replace(/^#/, ''), 16))
      .setTitle(`${selectedVibe.emoji} Vibe Check Results`)
      .setDescription(selectedIntro)
      .addFields(
        {
          name: '🎯 Current Vibe',
          value: selectedVibe.vibe,
          inline: true,
        },
        {
          name: '📊 Vibe Level',
          value: `${vibePercentage}%`,
          inline: true,
        },
        {
          name: '👤 Checked User',
          value: interaction.user.toString(),
          inline: true,
        },
        {
          name: '💭 Vibe Analysis',
          value: selectedVibe.description,
          inline: false,
        }
      )
      .setFooter({
        text: `Dynamic Innovative Studio • ${botConfig.version}`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'vibecheck' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content:
        '✨ The vibe-o-meter is experiencing technical difficulties. Please try again later!',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('vibecheck', _execute, {
    manualCooldown: true,
  }),
};
