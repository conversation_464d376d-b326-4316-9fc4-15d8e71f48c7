/**
 * @file COMMAND HANDLER.JS
 *
 * @version 1.0.2
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Command execution middleware for Discord bot commands.
 * Handles cooldowns, logging, error handling, and safe replies.
 * Provides factories for common command patterns (standard, admin, hr).
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { MessageFlags } from 'discord.js';

import {
  getCooldownMs,
  getRemaining,
  isExpired,
  set,
} from '../utils/cooldown.js';
import { handleError } from '../utils/errorHandler.js';
import logger from '../utils/logger.js';
import { safeReply } from '../utils/replyHelpers.js';
import {
  hasAdminPermission,
  hasHumanResourcesPermission,
} from '../utils/validators.js';

// ------------ TYPE SAFETY ANNOTATIONS
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').Client} DiscordClient
 * @typedef {(interaction: ChatInputCommandInteraction, client: DiscordClient) => Promise<any>} CommandExecuteFn
 * @typedef {Object} CommandHandlerOptions
 * @property {boolean} [manualCooldown] - If true, cooldown is managed manually by the command
 * @property {boolean} [requireAdmin] - If true, restricts command to admins
 * @property {boolean} [requireHR] - If true, restricts command to HR
 */

// ------------ MAIN MIDDLEWARE FUNCTION
/**
 * Wraps a command execution function with standard middleware.
 * Handles cooldowns, logging, error handling, and safe replies.
 *
 * @param {string} commandName - Name of the command (for logging/cooldown)
 * @param {CommandExecuteFn} executeFn - The command's execute function
 * @param {CommandHandlerOptions} [options={}] - Middleware options
 * @returns {CommandExecuteFn}
 */
export function withCommandHandler(commandName, executeFn, options = {}) {
  return async (interaction, client) => {
    // Only handle ChatInputCommandInteraction
    if (
      !interaction.isChatInputCommand?.() &&
      interaction.commandName === undefined
    ) {
      return;
    }
    try {
      // Permission checks
      if (
        options.requireAdmin &&
        (!interaction.member ||
          !hasAdminPermission(/** @type {any} */ (interaction.member)))
      ) {
        return safeReply(interaction, {
          content: '🚫 **Access Denied!** (Admin only)',
          flags: [MessageFlags.Ephemeral],
        });
      }
      if (
        options.requireHR &&
        (!interaction.member ||
          !hasHumanResourcesPermission(/** @type {any} */ (interaction.member)))
      ) {
        return safeReply(interaction, {
          content: '🚫 **Access Denied!** (HR only)',
          flags: [MessageFlags.Ephemeral],
        });
      }

      // Command cooldown check
      const cooldownKey = `${commandName}:${interaction.user.id}`;
      const commandCooldown = getCooldownMs(commandName);

      // Manual cooldown option for commands like broadcast
      if (options.manualCooldown) {
        if (!isExpired(cooldownKey, commandCooldown)) {
          const remaining = Math.ceil(
            getRemaining(cooldownKey, commandCooldown) / 1000
          );
          return safeReply(interaction, {
            content: `⏳ Please wait ${remaining}s before using this command again.`,
            flags: [MessageFlags.Ephemeral],
          });
        }
        // Only set cooldown when command is actually executed
      } else if (!isExpired(cooldownKey, commandCooldown)) {
        const remaining = Math.ceil(
          getRemaining(cooldownKey, commandCooldown) / 1000
        );
        return safeReply(interaction, {
          content: `⏳ Please wait ${remaining}s before using this command again.`,
          flags: [MessageFlags.Ephemeral],
        });
      }

      // Structured logging for command usage
      logger.info(
        {
          event: 'command_execute',
          command: commandName,
          user: interaction.user.id,
          username: interaction.user.tag,
          guild: interaction.guild?.id,
          options:
            'options' in interaction
              ? JSON.stringify(interaction.options.data)
              : undefined,
        },
        `${commandName} command used by ${interaction.user.tag}`
      );

      // Execute the command
      await executeFn(interaction, client);

      // Set cooldown after successful execution
      set(cooldownKey);
    } catch (error) {
      // Error handling with context
      logger.error(
        {
          event: 'command_error',
          command: commandName,
          user: interaction.user.id,
          username: interaction.user.tag,
          guild: interaction.guild?.id,
          error: error instanceof Error ? error.message : String(error),
        },
        `Error in ${commandName} command`
      );

      const err = error instanceof Error ? error : new Error(String(error));
      await handleError(err, { command: commandName }, client);
      await safeReply(interaction, {
        content: '⚠️ An error occurred while processing this command.',
        flags: [MessageFlags.Ephemeral],
      });
    }
  };
}

// ------------ COMMAND HANDLER FACTORY
/**
 * Factory for creating common command patterns.
 * - standard: Standard command with cooldown and error handling.
 * - admin: Admin command with security checks.
 * - hr: Human Resources command with security checks.
 */
export const CommandHandler = {
  /**
   * Standard command with cooldown and error handling.
   * @param {string} commandName
   * @param {CommandExecuteFn} executeFn
   * @param {CommandHandlerOptions} [options]
   * @returns {CommandExecuteFn}
   */
  standard: (commandName, executeFn, options = {}) =>
    withCommandHandler(commandName, executeFn, options),

  /**
   * Admin command with security checks.
   * @param {string} commandName
   * @param {CommandExecuteFn} executeFn
   * @returns {CommandExecuteFn}
   */
  admin: (commandName, executeFn) =>
    withCommandHandler(commandName, executeFn, {
      requireAdmin: true,
    }),

  /**
   * Human Resources command with security checks.
   * @param {string} commandName
   * @param {CommandExecuteFn} executeFn
   * @returns {CommandExecuteFn}
   */
  hr: (commandName, executeFn) =>
    withCommandHandler(commandName, executeFn, {
      requireHR: true,
    }),

  /**
   * Admin and Human Resources command with security checks.
   * @param {string} commandName
   * @param {CommandExecuteFn} executeFn
   * @returns {CommandExecuteFn}
   */
  hrAndAdmin: (commandName, executeFn) =>
    withCommandHandler(commandName, executeFn, {
      requireHR: true,
      requireAdmin: true,
    }),
};
