/**
 * @file MESSAGE.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Message model for storing and managing message data.
 * Provides a class for representing messages exchanged with users,
 * including methods for creating, formatting, and converting messages.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {Object} MessageData
 * @property {string} id
 * @property {string} userId
 * @property {string} content
 * @property {'incoming'|'outgoing'} type
 * @property {string|undefined} [authorId]
 * @property {Date|undefined} [timestamp]
 * @property {Object|undefined} [metadata]
 */

// ------------ MESSAGE CLASS
/**
 * Message class representing a message exchanged with a user.
 * Handles construction, conversion, and utility methods for messages.
 */
export class Message {
  /**
   * Create a new Message instance.
   * @param {MessageData} messageData - Message data object
   */
  constructor(messageData) {
    /** @type {string} */
    this.id = messageData.id;

    /** @type {string} */
    this.userId = messageData.userId;

    /** @type {string} */
    this.content = messageData.content;

    /** @type {'incoming'|'outgoing'} */
    this.type = messageData.type;

    /** @type {string|undefined} */
    this.authorId = messageData.authorId;

    /** @type {Date} */
    this.timestamp = messageData.timestamp || new Date();

    /** @type {Object} */
    this.metadata = messageData.metadata || {};
  }

  /**
   * Convert Message instance to a plain object for storage.
   * @returns {MessageData} Plain object representation of the message
   */
  toObject() {
    return {
      id: this.id,
      userId: this.userId,
      content: this.content,
      type: this.type,
      authorId: this.authorId,
      timestamp: this.timestamp,
      metadata: this.metadata,
    };
  }

  /**
   * Create a Message instance for a user's incoming message.
   * @param {string} userId - Discord user ID
   * @param {string} content - Message content
   * @param {Object} [metadata={}] - Additional metadata
   * @returns {Message}
   */
  static createIncoming(userId, content, metadata = {}) {
    return new Message({
      id: `${userId}-${Date.now()}`,
      userId,
      content,
      type: 'incoming',
      timestamp: new Date(),
      metadata,
    });
  }

  /**
   * Create a Message instance for an outgoing message (from bot or admin).
   * @param {string} userId - Discord user ID (recipient)
   * @param {string} content - Message content
   * @param {string|null} [authorId=null] - Discord user ID of author (null for bot)
   * @param {Object} [metadata={}] - Additional metadata
   * @returns {Message}
   */
  static createOutgoing(userId, content, authorId = undefined, metadata = {}) {
    return new Message({
      id: `${userId}-${Date.now()}`,
      userId,
      content,
      type: 'outgoing',
      authorId: authorId == null ? undefined : authorId,
      timestamp: new Date(),
      metadata,
    });
  }

  /**
   * Create a Message instance from MySQL data.
   * Converts MySQL timestamp to Date and parses fields as needed.
   * @param {Partial<MessageData>} data - Data from MySQL
   * @returns {Message}
   */
  static fromMySQL(data) {
    if (
      !data ||
      typeof data !== 'object' ||
      typeof data.id !== 'string' ||
      typeof data.userId !== 'string' ||
      typeof data.content !== 'string' ||
      (data.type !== 'incoming' && data.type !== 'outgoing')
    ) {
      throw new TypeError('Invalid message data from MySQL');
    }

    return new Message({
      id: data.id,
      userId: data.userId,
      content: data.content,
      type: data.type,
      authorId: data.authorId ?? undefined,
      timestamp: data.timestamp ? new Date(data.timestamp) : new Date(), // fallback to now
      metadata: data.metadata ?? {},
    });
  }
}

// ------------ EXPORT
export default Message;
